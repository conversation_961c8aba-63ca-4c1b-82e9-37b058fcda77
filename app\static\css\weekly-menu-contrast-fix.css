/* 
 * 周菜单列表对比度修复专用样式
 * 解决背景色与文字同色的问题
 */

/* ===== 全局badge样式重置 ===== */
.badge {
    display: inline-block !important;
    padding: 0.375rem 0.75rem !important;
    margin-bottom: 0 !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    line-height: 1 !important;
    color: #fff !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: baseline !important;
    border-radius: 0.375rem !important;
    border: 1px solid transparent !important;
    text-shadow: none !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.15s ease-in-out !important;
}

/* ===== 状态特定样式 ===== */

/* 计划中状态 - 灰色系 */
.badge-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
    border-color: #5a6268 !important;
}

/* 已发布状态 - 红色系 */
.badge-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    border-color: #bd2130 !important;
}

/* 成功状态 - 绿色系 */
.badge-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border-color: #1e7e34 !important;
}

/* 警告状态 - 黄色系（特殊处理） */
.badge-warning {
    background-color: #ffc107 !important;
    color: #212529 !important; /* 深色文字确保对比度 */
    border-color: #d39e00 !important;
    font-weight: 700 !important; /* 加粗字体增强可读性 */
}

/* 信息状态 - 蓝色系 */
.badge-info {
    background-color: #17a2b8 !important;
    color: #ffffff !important;
    border-color: #117a8b !important;
}

/* ===== 表格内badge特殊优化 ===== */
.table .badge {
    font-size: 0.8125rem !important; /* 13px */
    padding: 0.25rem 0.5rem !important;
    min-width: 60px !important; /* 确保最小宽度 */
    text-align: center !important;
}

/* ===== 悬停效果 ===== */
.badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    cursor: default !important;
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
    .badge {
        border-width: 2px !important;
        font-weight: 700 !important;
    }
    
    .badge-warning {
        background-color: #ffcd39 !important;
        color: #000000 !important;
        border-color: #000000 !important;
    }
    
    .badge-info {
        background-color: #0d6efd !important;
        color: #ffffff !important;
        border-color: #ffffff !important;
    }
}

/* ===== 深色主题支持 ===== */
@media (prefers-color-scheme: dark) {
    .badge-warning {
        background-color: #ffcd39 !important;
        color: #000000 !important;
        border-color: #ffc107 !important;
    }
    
    .badge-secondary {
        background-color: #adb5bd !important;
        color: #000000 !important;
        border-color: #6c757d !important;
    }
}

/* ===== 移动端优化 ===== */
@media (max-width: 768px) {
    .badge {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        min-width: 50px !important;
    }
}

/* ===== 打印样式 ===== */
@media print {
    .badge {
        background-color: transparent !important;
        color: #000000 !important;
        border: 1px solid #000000 !important;
        box-shadow: none !important;
    }
    
    .badge-warning {
        background-color: #f8f9fa !important;
        color: #000000 !important;
    }
}

/* ===== 无障碍支持 ===== */
.badge[aria-label]::after {
    content: " (" attr(aria-label) ")";
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* ===== 强制覆盖可能的冲突样式 ===== */
.table .badge,
.card .badge,
.list-group .badge,
div .badge,
span .badge {
    background-color: inherit !important;
    color: inherit !important;
    border-color: inherit !important;
}

/* 重新应用正确的状态颜色 */
.table .badge-secondary,
.card .badge-secondary,
.list-group .badge-secondary,
div .badge-secondary,
span .badge-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
    border-color: #5a6268 !important;
}

.table .badge-danger,
.card .badge-danger,
.list-group .badge-danger,
div .badge-danger,
span .badge-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    border-color: #bd2130 !important;
}

.table .badge-warning,
.card .badge-warning,
.list-group .badge-warning,
div .badge-warning,
span .badge-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border-color: #d39e00 !important;
    font-weight: 700 !important;
}

.table .badge-success,
.card .badge-success,
.list-group .badge-success,
div .badge-success,
span .badge-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border-color: #1e7e34 !important;
}

.table .badge-info,
.card .badge-info,
.list-group .badge-info,
div .badge-info,
span .badge-info {
    background-color: #17a2b8 !important;
    color: #ffffff !important;
    border-color: #117a8b !important;
}
